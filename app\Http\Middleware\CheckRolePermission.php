<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Symfony\Component\HttpFoundation\Response;

class CheckRolePermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // return $next($request);

        if (!Auth::check()) {
            return to_route('login');
        }

        $user = Auth::user();

        // Get current route name or action
        $routeName = Route::currentRouteName(); // e.g., 'members.index , admin-profile'

        // Optionally: map routeName => permission_slug from DB
        if ($routeName) {
            $requiredPermission = $this->getPermissionForRoute($routeName);

            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Permission denied');
            }
        }

        return $next($request);
    }

    protected function getPermissionForRoute($routeName)
    {
        // Example static mapping (can be loaded from config or DB later)
        $map = config('permissions.members') + config('permissions.whatsapps');

        return $map[$routeName] ?? null;
    }
}
