@forelse($records as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ ucfirst($row->name ?? '') }}</td>
        <td>{{ $row->code ?? '' }}</td>
        <td>{{ ucfirst($row->type ?? '') }}</td>
        <td  class="text-center">{{ $row->created_at->format('d M Y, h:i A') }}</td>
        <td class="text-center">
            @if ($row->status == 'active')
                <span class="badge bg-success">{{ ucfirst($row->status) }}</span>
            @else
                <span class="badge bg-danger">{{ ucfirst($row->status ?? '') }}</span>
            @endif
        </td>
        <td class="d-flex justify-content-center gap-2">
            <button class="btn btn-sm btn-outline-warning" title="Edit Subject" onclick="editSubject({{ $row->id }})">
                <i class="fa-solid fa-pen-to-square"></i>
                <input type="hidden" id="edit_subject_name_data{{ $row->id }}" value="{{ $row->name ?? '' }}">
                <input type="hidden" id="edit_subject_code_data{{ $row->id }}" value="{{ $row->code ?? '' }}">
                <input type="hidden" id="edit_subject_type_data{{ $row->id }}" value="{{ $row->type }}">
                <input type="hidden" id="edit_subject_status_data{{ $row->id }}" value="{{ $row->status }}">
            </button>
            <button class="btn btn-sm btn-outline-danger" type="button" title="Delete Subject" onclick="deleteSubject({{ $row->id }})">
                <i class="fa-solid fa-trash"></i>
            </button>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="7" class="text-center">No Subjects found.</td>
    </tr>
@endforelse