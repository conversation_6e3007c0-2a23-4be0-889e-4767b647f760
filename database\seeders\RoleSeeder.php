<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if(Role::count() > 0){
            return;
        }

        $roles = [
            ['name' => 'Super Administrator', 'slug' => 'superadmin'],
            ['name' => 'Administrator', 'slug' => 'admin'],
            ['name' => 'Teacher', 'slug' => 'teacher'],
            ['name' => 'Student', 'slug' => 'student'],
            ['name' => 'Parent', 'slug' => 'parent'],
        ];

        foreach ($roles as $role) {
            Role::Create($role);
        }
    }
}
