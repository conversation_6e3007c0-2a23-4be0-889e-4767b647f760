<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>{{ config('constant.app_name') }} - Login</title>
    <link rel="icon" type="image/jpg" href="{{ asset('logo_favicon.jpg') }}">
    
    <style>
        body {
        background: linear-gradient(135deg, #6f42c1, #6610f2);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-card {
        width: 100%;
        max-width: 380px;
        background-color: #fff;
        border-radius: 16px;
        padding: 30px 25px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .form-control:focus {
        box-shadow: none;
        border-color: #6610f2;
        }

        .login-btn {
        background-color: #007bff;
        border: none;
        }

        .login-btn:hover {
        background-color: #0069d9;
        }

        .text-muted a {
        color: #007bff;
        text-decoration: none;
        }

        .text-muted a:hover {
        text-decoration: underline;
        }

        @media (max-width: 420px) {
        .login-card {
            padding: 25px 20px;
        }
        }
    </style>

    <link rel="stylesheet" href="{{ asset('assets/bootstrap/bootstrap.min.css') }}">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">

    {{-- Jquery --}}
    <script src="{{ asset('assets/jquery/jquery.min.js') }}"></script>
</head>
<body>
    <div class="login-card">
        <div class="text-center mb-4">
            <h4 class="fw-bold">
                {{-- Welcome Back  --}}
                {{ config('constant.app_name') }}</h4>
            <p class="text-muted mb-0">Please login to your account</p>

            <x-alert />
        </div>

        <form class="login-form" action="{{ route('login.submit') }}" method="POST">
            @csrf
            <div class="mb-3">
                <label for="email" class="form-label">Email address</label>
                <input type="email" name="email" class="form-control" placeholder="<EMAIL>" required>
            </div>

            <div class="mb-3">
                <div class="d-flex justify-content-between">
                <label for="password" class="form-label">Password</label>
                {{-- <a href="#" class="small text-muted">Forgot?</a> --}}
                </div>
                <input type="password" name="password" class="form-control" placeholder="Enter your password" required>
            </div>

            <div class="d-grid mb-3">
                <button type="submit" class="btn btn-primary login-btn">Login</button>
            </div>

            <div class="text-center">
                <small class="text-muted">&copy; {{ date('Y') }} {{ config('constant.footer_company_name') }}. All rights reserved.</small>
            </div>
        </form>
    </div>

    <script src="{{ asset('assets/bootstrap/bootstrap.min.js') }}"></script>
</body>
</html>
