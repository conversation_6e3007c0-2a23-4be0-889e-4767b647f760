<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class AuthController extends Controller
{
    public function login(){
        return view('admin.auth.login');
    }
    
    public function loginSubmit(Request $request){
        $validated = $request->validate([
            'email' => 'required|email|exists:users,email',
            'password' => 'required|string|min:8|max:20',
        ]);

        if(Auth::attempt(['email' => $validated['email'], 'password' => $validated['password']])){
        //    return Auth::user()->name;
            return to_route('dashboard')->with('success','Login Successfully!');
        }else{
            return "Password are not match";
        }
    }

    public function dashboard(){
        return view('admin.dashboard');
    }

    public function logout(){
        Auth::logout();
        session()->flush();
        return to_route('login')->with('success','Logout Successfully!');
    }
}
