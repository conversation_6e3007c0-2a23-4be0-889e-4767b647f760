<?php

namespace App\Http\Controllers;

use App\Models\Section;
use App\Models\SchoolClass;
use Illuminate\Validation\Rule;
use Illuminate\Http\Request;

class SectionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $classes = SchoolClass::pluck('name','id');
        $records = Section::latest();
        if(isset($request->name)){
            $records = $records->where('name','LIKE', "%$request->name%");
        }
        if(isset($request->class_name)){
            $records = $records->whereHas('class', function ($query) use ($request) {
                $query->where('name', 'LIKE', '%' . $request->class_name . '%');
            });
        }
        $records = $records->paginate(config('constant.paginate'));
        if ($request->ajax()) {
            $view = view('admin.sections.index-data', compact('records'))->render();
            $pagination = view('pagination.links', compact('records'))->render();
            return response()->json([
                'html'       => $view,
                'pagination' => $pagination,
            ]);
        }
        return view('admin.sections.index',compact('records','classes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'class_id' => 'required|exists:classes,id',
            'name' => [
                'required',
                'max:100',
                'string',
                Rule::unique('sections')->where(function ($query) use ($request) {
                    return $query->where('class_id', $request->class_id);
                }),
            ],
            'status' => 'required|in:active,inactive'
        ]);

        try {
            Section::create($request->only('class_id','name','status'));
            return response()->json(['status'=>true, 'message'=>'Section Added Successfully!'],200);
        } catch (\Exception $ex) {
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!', 'error'=>$ex->getMessage()],500);
        } 
    }

    /**
     * Display the specified resource.
     */
    public function show(Section $section)
    {
        return $section;
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Section $section)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Section $section)
    {
        $request->validate([
            'class_id' => 'required|exists:classes,id',
            'name' => [
                'required',
                'max:100',
                'string',
                Rule::unique('sections')->where(function ($query) use ($request) {
                    return $query->where('class_id', $request->class_id);
                })->ignore($section->id),
            ],
            'status' => 'required|in:active,inactive'
        ]);

        try {
            Section::where('id',$section->id)->update($request->only('class_id','name','status'));
            return response()->json(['status'=>true, 'message'=>'Section Updated Successfully!'],200);
        } catch (\Exception $ex) {
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!', 'error'=>$ex->getMessage()],500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        Section::where('id',$id)->delete();
        return response(['status'=>true, 'code'=>200,'message'=>'Section Deleted Successfully!']);
    }
}
