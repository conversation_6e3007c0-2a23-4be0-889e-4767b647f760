@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Classes')

@section('content')
    <x-alert />
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Classes Management</h4>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addClassModal">
                <i class="fas fa-plus"></i> Add Class
            </button>
        </div>

        <!-- Filter Input -->
        <div class="mb-3">
            <input type="text" class="form-control" id="Classesearch" placeholder="Search Class by Name...">
        </div>

        <!-- Classes Table -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="ClassesTable">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>Class Name</th>
                        <th class="text-center">Created At</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($records as $row)
                        <tr>
                            <td>{{ $loop->iteration }}</td>
                            <td>{{ $row->name ?? '' }}</td>
                            <td  class="text-center">{{ $row->created_at->format('d M Y, h:i A') }}</td>
                            <td class="text-center">
                                @if ($row->status == 'active')
                                    <span class="badge bg-success">{{ ucfirst($row->status) }}</span>
                                @else
                                    <span class="badge bg-danger">{{ ucfirst($row->status ?? '') }}</span>
                                @endif
                            </td>
                            <td class="d-flex justify-content-center gap-2">
                                <button class="btn btn-sm btn-outline-warning" title="Edit Class" data-bs-toggle="modal" data-bs-target="#editClassModal{{ $row->id }}">
                                    <i class="fa-solid fa-pen-to-square"></i>
                                </button>
                                <form action="{{ route('classes.destroy',$row->id) }}" method="POST" id="delete-form-{{ $row->id }}" style="display:inline;">
                                    @csrf
                                    @method('DELETE')
                                    <button class="btn btn-sm btn-outline-danger" type="button" title="Delete Class" onclick="deleteData({{ $row->id }})">
                                        <i class="fa-solid fa-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="text-center">No Classes found.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
            <div class="mt-2">
                {{ $records->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>

    <!-- Add Class Modal -->
    <div class="modal fade" id="addClassModal" tabindex="-1" aria-labelledby="addRoleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <form method="POST" action="{{ route('classes.store') }}">
                @csrf
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="addRoleModalLabel">Add New Class</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="form_mode" value="create">
                        <div class="mb-3">
                            <label for="roleName" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control  @error('name') is-invalid @enderror" id="roleName" name="name" value="{{ old('name') }}" maxlength="50" required>
                            @error('name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="mb-3">
                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select name="status" id="status" class="form-select @error('status') is-invalid @enderror"" required>
                                <option value="active" @selected(old('status') == "active")>Active</option>
                                <option value="inactive" @selected(old('status') == "inactive")>Inactive</option>
                            </select>
                            @error('status') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-success">Save Class</button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    @foreach ($records as $row)
        <!-- Edit Class Modal -->
        <div class="modal fade" id="editClassModal{{ $row->id }}" tabindex="-1" aria-labelledby="editClassModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <form method="POST" action="{{ route('classes.update',$row->id) }}">
                    @csrf
                    @method('PUT')
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="editClassModalLabel">Update Class</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <input type="hidden" name="form_mode" value="edit">
                            <input type="hidden" name="edit_id" value="{{ $row->id }}">
                            <div class="mb-3">
                                <label for="roleName" class="form-label">Class Name</label>
                                <input type="text" class="form-control  @error('name') is-invalid @enderror" id="roleName" name="name" value="{{ old('name',$row->name ?? '') }}" maxlength="100" required>
                                @error('name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>
                            <div class="mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select name="status" id="status" class="form-select @error('status') is-invalid @enderror" required>
                                    <option value="active" @selected(old('status', $row->status ?? '') == "active")>Active</option>
                                    <option value="inactive" @selected(old('status', $row->statys ?? '') == "inactive")>Inactive</option>
                                </select>
                                @error('status') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn btn-success">Update Class</button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    @endforeach
@endsection

@push('scripts')
    <script>
        // Search Filter
        $(document).ready(function () {
            $("#Classesearch").on("keyup", function () {
                const value = $(this).val().toLowerCase();
                $("#ClassesTable tbody tr").filter(function () {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });
        });
    </script>
    @if ($errors->any())
        <script>
            $(document).ready(function() {
                @if (old('form_mode') === 'create')
                    $('#addClassModal').modal('show');
                @elseif (old('form_mode') === 'edit')
                    $('#editClassModal{{ old('edit_id') }}').modal('show');
                @endif
            });
        </script>
    @endif

    <x-delete-alert />
@endpush
