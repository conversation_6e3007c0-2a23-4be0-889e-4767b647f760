@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Subjects')

@section('content')
    <x-alert />
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Subjects Management</h4>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSubjectModal">
                <i class="fas fa-plus"></i> Add Section
            </button>
        </div>

        <!-- Modern Filter UI -->
        <div class="row align-items-center g-2 mb-4">
            <div class="col-md-4">
                <input type="text" class="form-control col-md-4" id="section_class_name" placeholder="Search Section by Class Name...">
            </div>
            <div class="col-md-4">
                <input type="text" class="form-control" id="section_name_search" placeholder="Search Section by Name...">
            </div>
            <div class="col-auto">
                <button class="btn btn-success px-4" onclick="search()">
                    <i class="fa fa-search me-1"></i> Search
                </button>
            </div>
            <div class="col-auto">
                <button class="btn btn-secondary px-4" onclick="resetSearchForm()">
                    <i class="fa fa-undo me-1"></i> Reset
                </button>
            </div>
        </div>


        <!-- Subjects Table -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="ClassesTable">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Code</th>
                        <th>Type</th>
                        <th class="text-center">Created At</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    @include('admin.subjects.index-data')
                </tbody>
            </table>
            <div class="mt-2" id="pagination_links">
                {{ $records->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>

    <!-- Edit Section Modal -->
    <div class="modal fade" id="editSectionModal" tabindex="-1" aria-labelledby="editSectionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editSectionModalLabel">Update Section</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="edit_section_id">
                    
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Section Name</label>
                        <input type="text" class="form-control" id="edit_section_name" name="name" value="{{ old('name',$row->name ?? '') }}" maxlength="100" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select name="status" id="edit_section_status" class="form-select" required>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success" onclick="updateSection()">Update Section</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function search(){
            let name = $('#section_name_search').val();
            let class_name = $('#section_class_name').val();

            $.ajax({
                type: "GET",
                url: "{{ route('subjects.index') }}",
                data: {
                    'name': name,
                    'class_name': class_name
                },
                success: function (response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        }

        // Reset search form
        function resetSearchForm(){
            $('#section_name_search').val('');
            $('#section_class_name').val('');
            search();
        }

        // Handle pagination link clicks
        $(document).on('click', '#pagination_links a', function(e){
            e.preventDefault();
            let url = $(this).attr('href');
            let name = $('#section_name_search').val();
            let class_name = $('#section_class_name').val();
            
            $.ajax({
                url: url,
                type: "GET",
                data: {
                    'name': name,
                    'class_name': class_name
                },
                success: function(response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        });
    </script>

    <script>
        // Edit 
        function editSection(id){
            $('#edit_section_id').val(id);
            $('#edit_section_class_id').val($('#edit_section_class_id_data'+id).val());
            $('#edit_section_name').val($('#edit_section_name_data'+id).val());
            $('#edit_section_status').val($('#edit_section_status_data'+id).val());
            $('#editSectionModal').modal('show')
        }

        // Update
        function updateSubject(){
            const id = $('#edit_section_id').val();
            const class_id = $('#edit_section_class_id').val();
            const name = $('#edit_section_name').val();
            const status = $('#edit_section_status').val();

            if(class_id == '' || name == '' || status == ''){
                alertify.alert().setting({
                    title: 'Error!',
                    message: "All field is required!"
                }).show();
                return false;
            }

            $.ajax({
                type: "POST",
                url: "/sections/"+id,
                data: {
                    'class_id': class_id,
                    'name': name,
                    'status': status,
                    '_method': 'PUT',
                },
                success: function (response) {
                    if(response.status == true){
                        search();
                        $('#editSectionModal').modal('hide');
                        $('#edit_section_class_id').val('');
                        $('#edit_section_name').val('');
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
                error: function (xhr) {
                    if (xhr.status === 422) {
                        let errors = xhr.responseJSON.errors;
                        let firstError = Object.values(errors)[0][0]; // first error message

                        alertify.alert()
                            .setting({
                                title: 'Validation Error',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + firstError
                            }).show();
                    } else {
                        alertify.alert()
                            .setting({
                                title: 'Server Error',
                                message: '<i class="fa-solid fa-triangle-exclamation text-danger me-2"></i> Something went wrong.'
                            }).show();
                    }
                }
            });
        }

        // 
        function deleteSubject(id) {
            alertify.confirm(
                'Confirm Delete',
                'Are you sure you want to delete?',
                function () {

                    // ✅ User clicked "Yes"
                    $.ajax({
                        type: "DELETE",
                        url: "/subjects/" + id, // or use: `{{ url('sections') }}/${id}`
                        success: function (response) {
                            if(response.code == 200){
                                alertify.success(response.message);
                                search();
                            }else{
                                alert('fail');
                            }
                        }
                    });
                    
                },
                function () {
                    // ❌ User clicked "No"
                    alertify.error('Delete cancelled');
                }
            ).set('labels', {ok: 'Yes', cancel: 'No'});
        }
    </script>

    @include('admin.subjects.add-modal')
@endpush
