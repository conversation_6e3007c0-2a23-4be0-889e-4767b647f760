<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // 👇 Route Middleware (use in routes)
        $middleware->alias([
            'check_role_permission' => \App\Http\Middleware\CheckRolePermission::class,
        ]);

        // 👇 Global Middleware (applied to every request)
        // $middleware->append([
        //     \App\Http\Middleware\CheckRolePermission::class,
        // ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
