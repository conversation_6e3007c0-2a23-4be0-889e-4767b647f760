@if (session()->has('success'))
    {{-- <div class="alert alert-success d-flex align-items-center" role="alert">
        <i class="fa-solid fa-circle-check me-2"></i>
        {{ session('success') }}
    </div> --}}

    <script>
        const successAlert = alertify.alert()
            .setting({
                title: 'Success!',
                message: `{!! '<i class="fa-solid fa-circle-check text-success me-2"></i>' . session('success') !!}`,
            }).show();
        setTimeout(function () {
            successAlert.close();
        }, 3000);
    </script>
@endif

@if (session()->has('warning'))
    {{-- <div class="alert alert-warning d-flex align-items-center" role="alert">
        <i class="fa-solid fa-triangle-exclamation me-2"></i>
        {{ session('warning') }}
    </div> --}}

    <script>
        const warningAlert = alertify.alert()
            .setting({
                title: 'Success!',
                message: `{!! '<i class="fa-solid fa-triangle-exclamation text-warning me-2"></i>' . session('warning') !!}`,
            }).show();
        setTimeout(function () {
            warningAlert.close();
        }, 3000);
    </script>
@endif

@if (session()->has('danger'))
    {{-- <div class="alert alert-danger d-flex align-items-center" role="alert">
        <i class="fa-solid fa-triangle-exclamation me-2"></i>
        {{ session('danger') }}
    </div> --}}

    <script>
        alertify.alert()
            .setting({
                title: 'Error!',
                // 'message': "{{ session('error') }}",
                message: `{!! '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' . session('danger') !!}`,
            }).show();
    </script>
@endif

@if($errors->any())
    {{-- <div class="alert alert-danger d-flex align-items-center" role="alert">
        <i class="fa-solid fa-triangle-exclamation me-2"></i>
        {{ $errors->first() }}
    </div> --}}

    <script>
        const validationError = alertify.alert()
            .setting({
                'title': 'Validation Error!',
                message: `{!! '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' . $errors->first() !!}`,
            }).show();
        setTimeout(function () {
            validationError.close();
        }, 3000);
    </script>
@endif

<script>
    // setTimeout(() => {
    //     $('.alert-success, .alert-warning').slideUp(500, function() {
    //         $(this).remove(); // This ensures it is removed only after slideUp finishes
    //     });
    // }, 5000);
</script>