<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if(User::count() > 0){
            return;
        }

        $users = [
            [
                'name' => 'Super Admin',
                'phone' => '9999999991',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'photo' => null,
                'status' => 'active',
            ],
            [
                'name' => 'Admin User',
                'phone' => '9999999992',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'photo' => null,
                'status' => 'active',
            ],
            [
                'name' => 'Teacher User',
                'phone' => '9999999993',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'photo' => null,
                'status' => 'active',
            ],
            [
                'name' => 'Student User',
                'phone' => '9999999994',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'photo' => null,
                'status' => 'active',
            ],
            [
                'name' => 'Parent User',
                'phone' => '9999999995',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'photo' => null,
                'status' => 'active',
            ],
        ];

        foreach ($users as $user) {
            User::create($user);
        }
    }
}
