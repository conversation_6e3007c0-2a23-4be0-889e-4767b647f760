<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <title>{{ config('constant.app_name') }} - @yield('title') </title>
    <link rel="icon" type="image/jpg" href="{{ asset('logo_favicon.jpg') }}">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Bootstrap 5 CSS -->
    <link rel="stylesheet" href="{{ asset('assets/bootstrap/bootstrap.min.css') }}">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            min-height: 100vh;
            overflow-x: hidden;
        }

        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
            width: 280px;
            overflow-y: auto;
            max-height: 100vh;
        }

        .sidebar a {
            color: #fff;
            text-decoration: none;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.collapsed:hover {
            background-color: #495057;
        }

        .sidebar .nav-link.active {
            background-color: #007bff;
        }

        .content {
            padding: 20px;
        }

        .sidebar .collapse-inner {
            padding-left: 1.5rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: absolute;
                z-index: 999;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 220px;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .overlay {
                display: none;
                position: fixed;
                top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 998;
            }

            .overlay.show {
                display: block;
            }
        }
    </style>

    @stack('styles')

    {{-- Select2 --}}
    <link rel="stylesheet" href="{{ asset('assets/select/select2.min.css') }}">
    <style>
        /* Match border, height, and padding for single Select2 fields */
        .select2-container--default .select2-selection--single {
            border: 1px solid #ced4da !important; /* same as Bootstrap input border */
            height: 38px !important; /* match input height */
            padding: 6px 12px;
            border-radius: 4px; /* optional: match input rounding */
            box-shadow: none !important;
        }

        /* Text alignment inside the dropdown */
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 24px !important;
            padding-left: 0px;
            padding-right: 0px;
        }

        /* Dropdown arrow alignment */
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px !important;
            top: 1px !important;
        }
    </style>

    {{-- Alertify JS --}}
    <link rel="stylesheet" href="{{ asset('assets/alertify/alertify.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/alertify/alertify-bootstrap-theme.min.css') }}">

    {{-- Jquery --}}
    <script src="{{ asset('assets/jquery/jquery.min.js') }}"></script>

    {{-- Alertify JS --}}
    <script src="{{ asset('assets/alertify/alertify.min.js') }}"></script>
</head>
<body class="d-flex flex-column min-vh-100"> {{-- footer 100vh after --}}

    <!-- Top Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <!-- Sidebar Toggle (Mobile Only) -->
            <button class="btn btn-outline-light d-lg-none me-2" id="toggleSidebar">
                <i class="fas fa-bars"></i>
            </button>

            <!-- Brand -->
            <a class="navbar-brand" href="#">{{ config('constant.app_name') }} Panel</a>

            <!-- Right Side Menu -->
            <div class="ms-auto">
                <ul class="navbar-nav align-items-center">
                    <!-- Admin Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-2"></i> Admin
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-key me-2"></i> Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="{{ route('logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i> Logout
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Sidebar + Content -->
    <div class="d-flex flex-grow-1">
        <!-- Sidebar -->
        <div id="sidebar" class="sidebar p-3 d-lg-block">
            {{-- <h5 class="text-white mb-4">School ERP</h5> --}}
            <ul class="nav flex-column" id="erpSidebar">

                <li class="nav-item">
                    <a href="#" class="nav-link"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>

                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#adminPanel" role="button">
                        <i class="fas fa-user-shield me-2"></i>Admin Panel
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <div class="collapse" id="adminPanel">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item"><a href="#" class="nav-link">User Management</a></li>
                            <li class="nav-item"><a href="#" class="nav-link">Roles & Permissions</a></li>
                            <li class="nav-item"><a href="#" class="nav-link">Notifications</a></li>
                        </ul>
                    </div>
                </li>

                <li class="nav-item">
                    @php
                        $academicActive = request()->routeIs('classes.index')
                        || request()->routeIs('sections.index')
                        || request()->routeIs('subjects.index');
                    @endphp
                    <a class="nav-link {{ $academicActive ? '' : 'collapsed' }}" 
                       data-bs-toggle="collapse" 
                       href="#academic" 
                       role="button"
                       aria-expanded="{{ $academicActive ? 'true' : 'false' }}">
                        <i class="fas fa-school me-2"></i>Academic Management
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <div class="collapse {{ $academicActive ? 'show' : '' }}" id="academic">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item"><a href="{{ route('classes.index') }}" class="nav-link 
                                {{ request()->routeIs('classes.index') ? 'active' : '' }}">Class</a></li>
                            <li class="nav-item">
                                <a href="{{ route('sections.index') }}" class="nav-link 
                                    {{ request()->routeIs('sections.index') ? 'active' : '' }}">Section</a>
                            </li>
                            <li class="nav-item"><a href="{{ route('subjects.index') }}" class="nav-link
                                {{ request()->routeIs('subjects.index') ? 'active' : '' }}">Subject </a></li>
                        </ul>
                    </div>
                </li>

                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-user-graduate me-2"></i>Student Management</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-chalkboard-teacher me-2"></i>Teacher Management</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-calendar-check me-2"></i>Attendance Management</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-file-invoice-dollar me-2"></i>Fees Management</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-poll me-2"></i>Exams & Results</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-book me-2"></i>Library Management</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-hotel me-2"></i>Hostel Management</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-bus me-2"></i>Transport Management</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-bullhorn me-2"></i>Communication</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-chart-line me-2"></i>Reports Module</a></li>

                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#authSecurity" role="button">
                        <i class="fas fa-lock me-2"></i>Authentication
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <div class="collapse" id="authSecurity">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item"><a href="#" class="nav-link">Login/Logout</a></li>
                            <li class="nav-item"><a href="#" class="nav-link">Password Reset</a></li>
                            <li class="nav-item"><a href="#" class="nav-link">2FA Setup</a></li>
                        </ul>
                    </div>
                </li>

                <li class="nav-item">
                    @php
                        $settingActive = request()->routeIs('roles.index');
                        // || request()->routeIs('permissions.index') 
                        // || request()->routeIs('users.index');
                    @endphp
                    <a class="nav-link {{ $settingActive ? '' : 'collapsed' }}" 
                       data-bs-toggle="collapse" 
                       href="#setting" 
                       role="button"
                       aria-expanded="{{ $settingActive ? 'true' : 'false' }}">
                        <i class="fas fa-lock me-2"></i>Setting
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <div class="collapse {{ $settingActive ? 'show' : '' }}" id="setting">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item"><a href="{{ route('roles.index') }}" class="nav-link 
                                {{ request()->routeIs('roles.index') ? 'active' : '' }}">Role Master</a></li>
                            <li class="nav-item"><a href="#" class="nav-link">Permission Master</a></li>
                            <li class="nav-item"><a href="#" class="nav-link">Users </a></li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>

        <!-- Overlay for mobile -->
        <div id="overlay" class="overlay"></div>

        <!-- Main Content -->
        <div class="content flex-grow-1">
            @yield('content')
        </div>
    </div>

    {{-- Footer --}}
    <footer class="bg-dark text-white mt-auto py-3">
        <div class="container-fluid text-center">
            <small>
                 &copy; {{ date('Y') }} {{ config('constant.footer_company_name') }}. All rights reserved.
            </small>
        </div>
    </footer>

    <!-- Bootstrap Bundle JS -->
    <script src="{{ asset('assets/bootstrap/bootstrap.min.js') }}"></script>

    <script>
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        const toggleBtn = document.getElementById('toggleSidebar');

        toggleBtn?.addEventListener('click', () => {
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        });

        overlay?.addEventListener('click', () => {
            sidebar.classList.remove('show');
            overlay.classList.remove('show');
        });
    </script>

    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    </script>

    @stack('scripts')

    <script>
        // Globally set
        alertify.set('notifier','position', 'top-right');
        alertify.defaults.theme.ok = "btn btn-outline-primary";
        alertify.defaults.theme.cancel = "btn btn-outline-secondary";

        // message
        // alertify.success('success');
        // alertify.warning('warning');
        // alertify.error('error');

        // alert for validation error
        // alertify.alert().setting({
        //     'title' : 'Validation Error!',
        //     'message' : 'Email is inavlid!'
        // }).show();

        
        // Auto-close after 5 second (5000 ms)
        // const alertInstance = alertify.alert()
            // .setting({
            //     'title': 'Validation Error!',
            //     'message': 'Email is invalid!',
            // }).show();
        // setTimeout(function () {
        //     alertInstance.close();
        // }, 5000);


        // confirm for delete
        // alertify.confirm('Confirm Delete?', 'Are you sure to delete?', function(){ 
        //     alertify.success('Ok') 
        // }, function(){
        //      alertify.error('Cancel')
        // });
    </script>

    {{-- Select2 --}}
    <script src="{{ asset('assets/select/select2.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('.select2').select2({
                placeholder: "Search here...",
                allowClear: true
            });
        });
    </script>
</body>
</html>