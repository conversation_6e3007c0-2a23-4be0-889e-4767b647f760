@forelse($records as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $row->class->name ?? '' }}</td>
        <td>{{ $row->name ?? '' }}</td>
        <td  class="text-center">{{ $row->created_at->format('d M Y, h:i A') }}</td>
        <td class="text-center">
            @if ($row->status == 'active')
                <span class="badge bg-success">{{ ucfirst($row->status) }}</span>
            @else
                <span class="badge bg-danger">{{ ucfirst($row->status ?? '') }}</span>
            @endif
        </td>
        <td class="d-flex justify-content-center gap-2">
            <button class="btn btn-sm btn-outline-warning" title="Edit Section" onclick="editSection({{ $row->id }})">
                <i class="fa-solid fa-pen-to-square"></i>
                <input type="hidden" id="edit_section_class_id_data{{ $row->id }}" value="{{ $row->class->id ?? '' }}">
                <input type="hidden" id="edit_section_name_data{{ $row->id }}" value="{{ $row->name ?? '' }}">
                <input type="hidden" id="edit_section_status_data{{ $row->id }}" value="{{ $row->status }}">
            </button>
            <button class="btn btn-sm btn-outline-danger" type="button" title="Delete Section" onclick="deleteSection({{ $row->id }})">
                <i class="fa-solid fa-trash"></i>
            </button>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="6" class="text-center">No Sections found.</td>
    </tr>
@endforelse