<?php

namespace App\Http\Controllers;

use App\Models\SchoolClass;
use Illuminate\Validation\Rule;
use Illuminate\Http\Request;

class SchoolClassController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $records = SchoolClass::latest()->paginate(config('constant.master_paginate'));
        return view('admin.classes.index', compact('records'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:50|unique:classes,name',
            'status' => 'required|string|in:active,inactive',
        ]);

        SchoolClass::create($request->only('name', 'slug'));

        return redirect()->back()->with('success', 'Class Created Successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(SchoolClass $class)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SchoolClass $class)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => [
                'required','string','max:50',
                Rule::unique('classes')->ignore($id),
            ],
            'status' => 'required|string|in:active,inactive',
        ]);

        SchoolClass::where('id',$id)->update($request->only('name', 'status'));

        return redirect()->back()->with('warning', 'Class Updated Successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SchoolClass $class)
    {
        $class->delete();
        return back()->with('success','Class Deleted Successfully!');
    }
}
