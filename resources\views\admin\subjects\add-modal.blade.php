<!-- Add Subject Modal -->
<div class="modal fade" id="addSubjectModal" tabindex="-1" aria-labelledby="addSubjectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addSubjectModalLabel">Add New Subject</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="subject_name" name="name">
                </div>
                <div class="mb-3">
                    <label for="code" class="form-label">Code <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="subject_code" name="code">
                </div>
                <div class="mb-3">
                    <label for="status" class="form-label">Type <span class="text-danger">*</span></label>
                    <select name="status" id="subject_type" class="form-select">
                        <option value="theory">Theory</option>
                        <option value="practical">Practical</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                    <select name="status" id="subject_status" class="form-select">
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-success" onclick="addSuubject()">Save Subject</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Store 
    function addSuubject(){
        const name = $('#subject_name').val();
        const code = $('#subject_code').val();
        const type = $('#subject_type').val();
        const status = $('#subject_status').val();

        if(name == '' || code == '' || type == '' || status == ''){
            alertify.alert().setting({
                title: 'Error!',
                message: "All field is required!"
            }).show();
            return false;
        }

        $.ajax({
            type: "POST",
            url: "{{ route('subjects.store') }}",
            data: {
                'name': name,
                'code': code,
                'type': type,
                'status': status,
            },
            success: function (response) {
                if(response.status == true){
                    search();
                    $('#addSubjectModal').modal('hide');
                    $('#subject_type').val('');
                    $('#subject_code').val('');
                    $('#subject_name').val('');
                    alertify.success(response.message);
                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
            error: function (xhr) {
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    let firstError = Object.values(errors)[0][0]; // first error message

                    alertify.alert()
                        .setting({
                            title: 'Validation Error',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + firstError
                        }).show();
                } else {
                    alertify.alert()
                        .setting({
                            title: 'Server Error',
                            message: '<i class="fa-solid fa-triangle-exclamation text-danger me-2"></i> Something went wrong.'
                        }).show();
                }
            }
        });

    }
</script>