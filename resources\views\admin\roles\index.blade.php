@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Roles')

@section('content')
<x-alert />
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h4>Roles Management</h4>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRoleModal">
            <i class="fas fa-plus"></i> Add Role
        </button>
    </div>

    <!-- Filter Input -->
    <div class="mb-3">
        <input type="text" class="form-control" id="roleSearch" placeholder="Search Role by Name...">
    </div>

    <!-- Roles Table -->
    <div class="table-responsive">
        <table class="table table-bordered table-striped" id="rolesTable">
            <thead class="table-dark">
                <tr>
                    <th>#</th>
                    <th>Role Name</th>
                    <th>Slug</th>
                    <th>Created At</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                @forelse($roles as $role)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $role->name }}</td>
                        <td>{{ $role->slug }}</td>
                        <td>{{ $role->created_at->format('d M Y, h:i A') }}</td>
                        <td class="text-center">
                            <button class="btn btn-sm btn-outline-warning" title="Edit Role" data-bs-toggle="modal" data-bs-target="#editRoleModal{{ $role->id }}">
                                <i class="fa-solid fa-pen-to-square"></i>
                            </button>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5" class="text-center">No roles found.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
        <div class="mt-2">
            {{ $roles->links('pagination::bootstrap-5') }}
        </div>
    </div>
</div>

<!-- Add Role Modal -->
<div class="modal fade" id="addRoleModal" tabindex="-1" aria-labelledby="addRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <form method="POST" action="{{ route('roles.store') }}">
            @csrf
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="addRoleModalLabel">Add New Role</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="form_mode" value="create">
                    <div class="mb-3">
                        <label for="roleName" class="form-label">Role Name</label>
                        <input type="text" class="form-control  @error('name') is-invalid @enderror" id="roleName" name="name" value="{{ old('name') }}" maxlength="100" required>
                        @error('name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>
                    <div class="mb-3">
                        <label for="roleSlug" class="form-label">Role Slug</label>
                        <input type="text" class="form-control  @error('slug') is-invalid @enderror" id="roleSlug" name="slug" value="{{ old('slug') }}" maxlength="100" required>
                        @error('slug') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">Save Role</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </form>
    </div>
</div>

    @foreach ($roles as $role)
        <!-- Edit Role Modal -->
        <div class="modal fade" id="editRoleModal{{ $role->id }}" tabindex="-1" aria-labelledby="editRoleModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <form method="POST" action="{{ route('roles.update',$role->id) }}">
                    @csrf
                    @method('PUT')
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="editRoleModalLabel">Update New Role</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <input type="hidden" name="form_mode" value="edit">
                            <input type="hidden" name="edit_id" value="{{ $role->id }}">
                            <div class="mb-3">
                                <label for="roleName" class="form-label">Role Name</label>
                                <input type="text" class="form-control  @error('name') is-invalid @enderror" id="roleName" name="name" value="{{ old('name',$role->name ?? '') }}" maxlength="100" required>
                                @error('name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>
                            <div class="mb-3">
                                <label for="roleSlug" class="form-label">Role Slug</label>
                                <input type="text" class="form-control  @error('slug') is-invalid @enderror" id="roleSlug" name="slug" value="{{ old('slug',$role->slug ?? '') }}" maxlength="100" required>
                                @error('slug') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn btn-success">Update Role</button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    @endforeach
@endsection

@push('scripts')
    <script>
        // Search Filter
        $(document).ready(function () {
            $("#roleSearch").on("keyup", function () {
                const value = $(this).val().toLowerCase();
                $("#rolesTable tbody tr").filter(function () {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });
        });
    </script>
    @if ($errors->any())
        <script>
            $(document).ready(function() {
                @if (old('form_mode') === 'create')
                    $('#addRoleModal').modal('show');
                @elseif (old('form_mode') === 'edit')
                    $('#editRoleModal{{ old('edit_id') }}').modal('show');
                @endif
            });
        </script>
    @endif
@endpush
